# Docker Setup para wplace-proxy

Este documento explica como executar o projeto usando Docker e Docker Compose.

## Pré-requisitos

- Docker
- Docker Compose
- Make (opcional, para usar os comandos facilitados)

## Configuração Inicial

1. **Copie o arquivo de exemplo de variáveis de ambiente:**
   ```bash
   cp .env.example .env
   ```

2. **Edite o arquivo `.env` com suas configurações:**
   ```bash
   # IMPORTANTE: Mude o JWT_SECRET em produção!
   JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
   ```

## Executando o Projeto

### Desenvolvimento

```bash
# Usando Make (recomendado)
make dev

# Ou usando docker-compose diretamente
docker-compose up --build
```

### Produção

```bash
# Usando Make
make prod

# Ou usando docker-compose diretamente
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build
```

## Serviços Disponíveis

### Aplicação Principal
- **URL:** http://localhost:3000
- **Descrição:** Servidor principal do wplace-proxy

### Redis
- **Porta:** 6379
- **Descrição:** Banco de dados para armazenamento de artworks e alianças

### Redis Commander (apenas desenvolvimento)
- **URL:** http://localhost:8081
- **Descrição:** Interface web para administração do Redis

### Nginx (apenas produção)
- **Porta:** 80 (HTTP), 443 (HTTPS)
- **Descrição:** Reverse proxy com cache e rate limiting

## Comandos Úteis

### Usando Make

```bash
make help          # Mostra todos os comandos disponíveis
make build         # Constrói as imagens
make up            # Inicia os serviços
make down          # Para os serviços
make logs          # Mostra os logs
make clean         # Remove containers e volumes
make restart       # Reinicia os serviços
make redis-cli     # Conecta ao Redis CLI
make app-shell     # Acessa o shell da aplicação
make health        # Verifica a saúde dos serviços
make stats         # Mostra estatísticas dos containers
```

### Backup e Restore do Redis

```bash
# Fazer backup
make backup-redis

# Restaurar backup
make restore-redis BACKUP_FILE=backup-redis-20231201_120000.rdb
```

## Estrutura de Volumes

- `./artworks` → `/usr/src/app/artworks` - Imagens dos artworks
- `./tiles` → `/usr/src/app/tiles` - Tiles gerados
- `redis_data` → Volume persistente para dados do Redis

## Configurações de Ambiente

### Variáveis de Ambiente Principais

| Variável | Padrão | Descrição |
|----------|--------|-----------|
| `PORT` | 3000 | Porta da aplicação |
| `JWT_SECRET` | wplace | Chave secreta para JWT (MUDE EM PRODUÇÃO!) |
| `REDIS_URL` | redis://redis:6379 | URL de conexão com Redis |
| `NODE_ENV` | development | Ambiente de execução |

### Profiles do Docker Compose

- **development:** Inclui Redis Commander
- **production:** Inclui Nginx, exclui ferramentas de desenvolvimento

## Monitoramento e Logs

### Visualizar Logs
```bash
# Todos os serviços
docker-compose logs -f

# Serviço específico
docker-compose logs -f wplace-proxy
docker-compose logs -f redis
```

### Health Checks
```bash
# Verificar saúde da aplicação
curl http://localhost:3000/api/stats

# Verificar Redis
docker-compose exec redis redis-cli ping
```

### Estatísticas
```bash
# Estatísticas dos containers
docker stats $(docker-compose ps -q)

# Uso de recursos
docker system df
```

## Troubleshooting

### Problemas Comuns

1. **Porta já em uso:**
   ```bash
   # Verificar processos usando a porta
   lsof -i :3000
   
   # Ou mudar a porta no .env
   PORT=3001
   ```

2. **Redis não conecta:**
   ```bash
   # Verificar se o Redis está rodando
   docker-compose ps redis
   
   # Verificar logs do Redis
   docker-compose logs redis
   ```

3. **Permissões de arquivo:**
   ```bash
   # Ajustar permissões das pastas
   sudo chown -R $USER:$USER artworks tiles
   ```

4. **Limpar tudo e recomeçar:**
   ```bash
   make clean
   docker-compose up --build
   ```

### Logs Detalhados

Para debug mais detalhado, você pode:

1. **Acessar o container:**
   ```bash
   docker-compose exec wplace-proxy sh
   ```

2. **Verificar arquivos de configuração:**
   ```bash
   docker-compose exec wplace-proxy cat /usr/src/app/package.json
   ```

3. **Monitorar recursos:**
   ```bash
   docker-compose exec wplace-proxy top
   ```

## Configuração para Produção

### SSL/HTTPS

1. **Gere certificados SSL:**
   ```bash
   mkdir ssl
   # Coloque seus certificados em ssl/cert.pem e ssl/key.pem
   ```

2. **Descomente a configuração HTTPS no nginx.conf**

3. **Reinicie os serviços:**
   ```bash
   make prod
   ```

### Configurações de Segurança

1. **Mude o JWT_SECRET:**
   ```bash
   # Gere uma chave forte
   openssl rand -base64 32
   ```

2. **Configure senha do Redis (opcional):**
   ```bash
   # Descomente e configure no redis.conf
   requirepass your-redis-password-here
   ```

3. **Configure firewall:**
   ```bash
   # Exemplo com ufw
   sudo ufw allow 80
   sudo ufw allow 443
   sudo ufw deny 3000  # Bloquear acesso direto à aplicação
   sudo ufw deny 6379  # Bloquear acesso direto ao Redis
   ```

## Performance

### Otimizações Recomendadas

1. **Ajustar limites de memória do Redis:**
   ```bash
   # No redis.conf
   maxmemory 512mb
   ```

2. **Configurar cache do Nginx:**
   ```bash
   # Adicionar no nginx.conf
   proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=tiles:10m;
   ```

3. **Monitorar uso de recursos:**
   ```bash
   make stats
   ```

## Desenvolvimento

### Hot Reload

O modo de desenvolvimento inclui hot reload automático:

```bash
make dev
# Edite arquivos em src/ e veja as mudanças automaticamente
```

### Debug

Para debug da aplicação:

```bash
# Acessar shell do container
make app-shell

# Verificar variáveis de ambiente
env | grep -E "(PORT|JWT|REDIS)"

# Testar conexão com Redis
redis-cli -h redis ping
```
